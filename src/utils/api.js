import axios from 'axios';

// 使用相对路径，生产环境通过nginx转发，开发环境使用代理
const API_BASE_URL = process.env.NODE_ENV === 'production'
  ? '/api'  // 生产环境使用相对路径，通过nginx转发
  : 'http://localhost:5003'; // 本地开发环境直接指向后端

// 创建axios实例
const instance = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true,
  timeout: 1800000,
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Accept': 'application/json'
  },
  proxy: false
});

const API = {
  // 获取券机制列表
  async getCouponMechanisms(dateData) {
    try {
      const response = await instance.get('/get_coupon_mechanisms', { 
        params: dateData
      });
      return response.data;
    } catch (error) {

      return [];
    }
  },

  // 获取券门槛列表
  async getCouponThreshold(dateData) {
    try {
      const response = await instance.get('/get_coupon_threshold', { 
        params: dateData
      });
      return response.data;
    } catch (error) {

      return [];
    }
  },

  // 获取券折扣列表
  async getCouponDiscount(dateData) {
    try {
      const response = await instance.get('/get_coupon_discount', { 
        params: dateData
      });
      return response.data;
    } catch (error) {

      return [];
    }
  },

  // 获取子品牌列表
  async getSubBrands(dateData) {
    try {
      const response = await instance.get('/get_sub_brands', { 
        params: dateData
      });
      return response.data;
    } catch (error) {

      return [];
    }
  },

  // 获取省份列表
  async getProvinces(dateData) {
    try {
      const response = await instance.get('/get_provinces', { 
        params: dateData
      });
      return response.data;
    } catch (error) {

      return [];
    }
  },

  // 获取零售商列表
  async getRetailers(dateData) {
    try {
      const response = await instance.get('/get_retailers', { 
        params: dateData
      });
      return response.data;
    } catch (error) {

      return [];
    }
  },

  // 获取城市列表
  async getCities(dateData) {
    try {
      const response = await instance.get('/get_city', { 
        params: dateData
      });
      return response.data;
    } catch (error) {

      return [];
    }
  },

  // 获取平台列表
  async getPlatforms(dateData) {
    try {
      const response = await instance.get('/get_platform', { 
        params: dateData
      });
      return response.data;
    } catch (error) {

      return [];
    }
  },

  // 获取商品列表
  async getProducts(dateData) {
    try {
      const response = await instance.get('/get_products', { 
        params: dateData
      });
      return response.data;
    } catch (error) {

      return [];
    }
  },

  // 运行归因分析
  async runAttribution(data) {
    try {
      const formData = { ...data };
      if (!formData.brand && sessionStorage.getItem('brand')) {
        formData.brand = sessionStorage.getItem('brand');
      }
      
      const response = await instance.post('/run_attribution', formData);
      return response.data;
    } catch (error) {

      if (error.response) {
        return error.response.data;
      } else if (error.request) {
        return { 
          status: 'error', 
          message: '请求未收到响应，请检查网络连接' 
        };
      } else {
        return { 
          status: 'error', 
          message: error.message || '请求配置错误' 
        };
      }
    }
  },

  // 获取红黑榜TOP3
  async getTopRanking(data) {
    try {
      // 使用GET请求，将参数作为查询字符串传递
      const response = await instance.get('/get_result_data', { 
        params: data,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      return response.data;
    } catch (error) {

      if (error.response) {
        return error.response.data;
      } else if (error.request) {
        return { 
          status: 'error', 
          message: '请求未收到响应，请检查网络连接' 
        };
      } else {
        return { 
          status: 'error', 
          message: error.message || '请求配置错误' 
        };
      }
    }
  },

  // 维度下钻
  async drillDown(data) {
    try {
      const response = await instance.post('/drill_down', data);
      return response.data;
    } catch (error) {

      if (error.response) {
        return error.response.data;
      } else if (error.request) {
        return { 
          status: 'error', 
          message: '请求未收到响应，请检查网络连接' 
        };
      } else {
        return { 
          status: 'error', 
          message: error.message || '请求配置错误' 
        };
      }
    }
  },

  // 下载Excel
  downloadExcel(filePath) {
    // 如果提供了文件路径，则作为查询参数传递
    if (filePath) {
      // 生成下载文件名，使用当前时间戳确保唯一性
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace('T', '_').substring(0, 15);
      const fileName = `归因分析结果_${timestamp}.xlsx`;
      
      window.location.href = `${API_BASE_URL}/download?file_path=${encodeURIComponent(filePath)}&file_name=${encodeURIComponent(fileName)}`;
    } else {
      // 兼容旧的调用方式，不传参数，使用默认文件
      window.location.href = `${API_BASE_URL}/download`;
    }
  }
};

export default API; 